"""
Quiz Question Generator Script

This module generates quiz questions across different difficulty levels using a language model.
It supports:
- Dynamic prompt loading from a YAML file
- Fetching data from the model API
- Storing generated questions in a database

Functions:
- load_yaml_prompt: Load quiz prompts from a YAML file
- fetch_quiz_data: Retrieve quiz questions from the model
- ask_for_question: Main function to generate, adjust, and save quiz questions
"""

import asyncio
import json
import logging
import os
import time

import yaml

from api_client import query_model
from questions_logging import get_questions_by_level, insert_question_data

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


async def load_yaml_prompt(prompt):
    """
    Load and retrieve a specific prompt from a YAML file.

    Args:
        prompt (str): The key for the desired prompt in the YAML file.

    Returns:
        str: The retrieved prompt or an empty string if an error occurs.
    """
    yaml_prompt_file = "prompt.yaml"
    try:
        with open(yaml_prompt_file, "r", encoding="utf-8") as file:
            yaml_data = yaml.safe_load(file)
        return yaml_data.get("prompts", {}).get(prompt, "")
    except (FileNotFoundError, yaml.YAMLError) as e:
        logger.error("Error loading YAML file: %s", e)
        return ""


def add_metadata_to_questions(parsed_output, level, topic):
    """Add metadata to parsed questions"""
    for item in parsed_output:
        item["Level"] = level
        item["Topic"] = topic
    return parsed_output


async def fetch_quiz_data(quiz_prompt, model_id, level, topic, retries=2):
    """
    Fetch quiz data from the model for a specified level.

    Args:
        quiz_prompt (str): The formatted prompt string.
        model_id (str): The model identifier to query.
        level (str): Difficulty level (e.g., "easy", "intermediate", "advanced", "all").
        topic (str): The topic for the questions.
        retries (int): The number of times to retry in case of failure.

    Returns:
        list: Parsed quiz data as a list of dictionaries.
    """
    attempt = 0
    start_time = time.time()

    while attempt < retries:
        try:
            logger.info(
                "Fetching %s level quiz data (Attempt %d/%d)",
                level,
                attempt + 1,
                retries,
            )
            model_api_response = await query_model(quiz_prompt, model_id)

            if "choices" not in model_api_response:
                raise KeyError("'choices' key is missing in the model response.")

            output_json_string = model_api_response["choices"][0]["message"]["content"]

            start_index = output_json_string.find("[")
            end_index = output_json_string.rfind("]") + 1

            output_json = output_json_string[start_index:end_index]

            parsed_output = json.loads(output_json)

            # If level is "all", each question should already have its own level
            # Otherwise, use helper function to add metadata
            if level != "all":
                parsed_output = add_metadata_to_questions(parsed_output, level, topic)
            else:
                # For "all" level, just add topic if not already present
                for item in parsed_output:
                    if "Topic" not in item:
                        item["Topic"] = topic

            logger.info(
                "Fetched %s level questions in %.2f seconds",
                level,
                time.time() - start_time,
            )
            return parsed_output

        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error("Error fetching quiz data: %s", e)
            if os.getenv("DEBUG_LOG", "False").lower() == "true":
                logger.error(
                    "Model Response: %s",
                    (
                        model_api_response
                        if "model_api_response" in locals()
                        else "Unavailable"
                    ),
                )

            if attempt < retries - 1:
                logger.info("Sleeping for 5 seconds before Retrying...")
                time.sleep(5)
                logger.warning("Retrying... (%d/%d)", attempt + 2, retries)
            else:
                logger.error("Max retry attempts reached.")
                return []

        except (asyncio.TimeoutError, ConnectionError) as e:
            logger.error("Network error occurred: %s", e)
            if attempt < retries - 1:
                logger.info("Sleeping for 5 seconds before Retrying...")
                time.sleep(5)
                logger.warning("Retrying... (%d/%d)", attempt + 2, retries)
            else:
                logger.error("Max retry attempts reached.")
                return []

        except Exception as e:
            logger.exception("Unexpected error occurred: %s", e)
            return []

        attempt += 1


async def ask_for_question(quiz_name: str, topics: str, skill_id: int = None):
    """
    Main entry point to generate and save quiz questions for the specified quiz and topics.
    Generates questions for all difficulty levels in a single request.

    Args:
        quiz_name (str): Name of the quiz to generate.
        topics (str): List of topics for which quiz questions will be generated.
        skill_id (int, optional): The skill ID to associate with these questions.

    Returns:
        str: Name of the generated quiz if successful.
    """
    try:
        model_id = os.getenv("MODEL_ID")
        if not model_id:
            logger.error("MODEL_ID environment variable not set")
            return

        # Get question counts for each difficulty level from environment variables
        easy_questions = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
        intermediate_questions = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
        advanced_questions = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

        # Calculate total questions as the sum of all difficulty levels
        total_questions = easy_questions + intermediate_questions + advanced_questions

        logger.info(
            "Questions to generate - Easy: %d, Intermediate: %d, Advanced: %d, Total: %d",
            easy_questions,
            intermediate_questions,
            advanced_questions,
            total_questions,
        )

        logger.info("Starting quiz generation: %s for topics: %s", quiz_name, topics)

        # Load the prompt template for quiz generation
        quiz_prompt_template = await load_yaml_prompt("questions_prompt")
        if not quiz_prompt_template:
            logger.error("Failed to load context-gathering prompt.")
            return

        # Get existing questions for context
        context = []
        for level in ["easy", "intermediate", "advanced"]:
            level_questions = get_questions_by_level(quiz_name, level)
            if level_questions:
                context.extend(level_questions)

        context_str = "\n".join(context) if context else ""

        # Format the prompt with all necessary information
        quiz_prompt = quiz_prompt_template.format(
            no=total_questions, topics=topics, level="all", context=context_str
        )

        logger.info(
            f"Requesting {total_questions} questions across all difficulty levels in a single request"
        )

        # Fetch all questions in a single request
        all_questions = await fetch_quiz_data(quiz_prompt, model_id, "all", quiz_name)

        if not all_questions:
            logger.error("Failed to generate questions")
            return

        # Process and categorize questions by level
        questions_by_level = {"easy": [], "intermediate": [], "advanced": []}

        for question in all_questions:
            # Normalize level field
            level = question.get("Level", "").lower()
            if level == "easy":
                question["Level"] = "easy"
                questions_by_level["easy"].append(question)
            elif level == "intermediate":
                question["Level"] = "intermediate"
                questions_by_level["intermediate"].append(question)
            elif level in ["advanced", "advance"]:
                question["Level"] = "advanced"
                questions_by_level["advanced"].append(question)
            else:
                logger.warning(
                    f"Question has invalid level: {level}. Defaulting to 'easy'."
                )
                question["Level"] = "easy"
                questions_by_level["easy"].append(question)

            # Ensure topic is set
            question["Topic"] = quiz_name

        # Limit questions to the required number per level
        level_counts = {
            "easy": easy_questions,
            "intermediate": intermediate_questions,
            "advanced": advanced_questions,
        }

        for level in questions_by_level:
            target_count = level_counts.get(
                level, 10
            )  # Default to 10 if level not found
            if len(questions_by_level[level]) > target_count:
                questions_by_level[level] = questions_by_level[level][:target_count]
            elif len(questions_by_level[level]) < target_count:
                logger.warning(
                    f"Only generated {len(questions_by_level[level])} {level} questions, needed {target_count}"
                )

        # Combine all questions
        all_questions_to_insert = []
        for level in ["easy", "intermediate", "advanced"]:
            all_questions_to_insert.extend(questions_by_level[level])

        # Insert questions into database
        if all_questions_to_insert:
            insert_question_data(all_questions_to_insert, skill_id)
            logger.info(
                f"Successfully generated and saved {len(all_questions_to_insert)} questions:"
            )
            return quiz_name
        else:
            logger.error("No questions were generated")
            return None

    except Exception as e:
        logger.error(f"Error in ask_for_question: {e}", exc_info=True)
        return None
